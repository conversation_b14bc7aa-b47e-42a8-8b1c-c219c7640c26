import { ENV } from '@/lib/env';

const API_BASE_URL = ENV.VITE_API_URL || 'http://localhost:8000';

// Types for API responses
export interface UserClip {
  id: string;
  title: string;
  url: string;
  thumbnail: string;
  duration: string;
  views: number;
  likes: number;
  comments: number;
  vitalityScore: number;
  status: 'published' | 'processing' | 'draft';
  createdAt: string;
  platform: string;
  videoId: number;
  downloadCount?: number;
}

export interface UserStats {
  totalViews: number;
  totalVideos: number;
  totalClips: number;
  totalSubscribers: number;
  watchTime: number;
  credits: number;
}

export interface SocialPlatform {
  name: string;
  icon: string;
  connected: boolean;
  followers: string;
  profileUrl: string;
  color: string;
  bgColor: string;
}

export interface VideoCount {
  videos: number;
  clips: number;
}

/**
 * Get user's clips with metadata
 */
export const getUserClips = async (skip = 0, limit = 20): Promise<UserClip[]> => {
  try {
    const token = localStorage.getItem('auth_token');
    const response = await fetch(`${API_BASE_URL}/user/clips?skip=${skip}&limit=${limit}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch clips: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching user clips:', error);
    // Return mock data as fallback
    return [
      {
        id: "1",
        title: "Product Demo Highlight - AI Features Showcase",
        thumbnail: "https://images.unsplash.com/photo-1611162617213-7d7a39e9b1d7?q=80&w=300&h=200&auto=format&fit=crop",
        url: "#",
        duration: "45s",
        views: 12500,
        likes: 890,
        comments: 45,
        vitalityScore: 85,
        status: "published",
        createdAt: "2024-01-15",
        platform: "YouTube",
        videoId: 1,
        downloadCount: 0
      },
      {
        id: "2",
        title: "Tutorial Best Moment - Quick Tips",
        thumbnail: "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?q=80&w=300&h=200&auto=format&fit=crop",
        url: "#",
        duration: "32s",
        views: 8900,
        likes: 567,
        comments: 23,
        vitalityScore: 92,
        status: "published",
        createdAt: "2024-01-14",
        platform: "TikTok",
        videoId: 2,
        downloadCount: 0
      }
    ];
  }
};

/**
 * Get user statistics
 */
export const getUserStats = async (): Promise<UserStats> => {
  try {
    const token = localStorage.getItem('auth_token');
    const response = await fetch(`${API_BASE_URL}/user/stats`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch stats: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching user stats:', error);
    // Return mock data as fallback
    return {
      totalViews: 43700,
      totalVideos: 24,
      totalClips: 18,
      totalSubscribers: 1250,
      watchTime: 2840,
      credits: 100
    };
  }
};

/**
 * Get user's connected social platforms
 */
export const getUserSocialPlatforms = async (): Promise<SocialPlatform[]> => {
  try {
    const token = localStorage.getItem('auth_token');
    const response = await fetch(`${API_BASE_URL}/user/social-platforms`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch social platforms: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching social platforms:', error);
    // Return mock data as fallback
    return [
      {
        name: "YouTube",
        icon: "🎥",
        connected: true,
        followers: "12.5K",
        profileUrl: "#",
        color: "text-red-500",
        bgColor: "bg-red-50 dark:bg-red-900/20"
      },
      {
        name: "TikTok",
        icon: "🎵",
        connected: true,
        followers: "8.9K",
        profileUrl: "#",
        color: "text-black dark:text-white",
        bgColor: "bg-gray-50 dark:bg-gray-900/20"
      },
      {
        name: "Instagram",
        icon: "📸",
        connected: false,
        followers: "0",
        profileUrl: "#",
        color: "text-pink-500",
        bgColor: "bg-pink-50 dark:bg-pink-900/20"
      },
      {
        name: "Twitter",
        icon: "🐦",
        connected: false,
        followers: "0",
        profileUrl: "#",
        color: "text-blue-400",
        bgColor: "bg-blue-50 dark:bg-blue-900/20"
      }
    ];
  }
};

/**
 * Get user's video count
 */
export const getUserVideoCount = async (): Promise<VideoCount> => {
  try {
    const token = localStorage.getItem('auth_token');
    const response = await fetch(`${API_BASE_URL}/user/video-count`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch video count: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching video count:', error);
    // Return mock data as fallback
    return {
      videos: 24,
      clips: 18
    };
  }
};
