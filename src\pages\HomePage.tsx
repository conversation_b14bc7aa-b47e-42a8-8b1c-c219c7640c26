
import React, { useState, useEffect } from "react";
import { Navigate } from "react-router-dom";
import DashboardLayout from "@/components/Dashboard";
import { useAuth } from "@/context/AuthContext";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Scissors,
  VideoIcon,
  BookOpen,
  BarChart,
  Calendar,
  Settings,
  Image,
  Share2,
  Cloud,
  TrendingUp,
  Users,
  Play,
  Eye,
  Heart,
  MessageCircle,
  Upload,
  Edit,
  Plus,
  Filter,
  Search,
  MoreVertical,
  CheckCircle,
  Clock,
  AlertCircle,
  ArrowUp,
  ArrowDown,
  Zap
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import {
  getUserClips,
  getUserStats,
  getUserSocialPlatforms,
  type UserClip,
  type UserStats,
  type SocialPlatform
} from "@/services/userService";

const HomePage = () => {
  const { isAuthenticated, user, isLoading } = useAuth();
  const [activeTab, setActiveTab] = useState("overview");
  const [searchQuery, setSearchQuery] = useState("");
  const [clips, setClips] = useState<UserClip[]>([]);
  const [stats, setStats] = useState<UserStats>({
    totalViews: 0,
    totalVideos: 0,
    totalClips: 0,
    totalSubscribers: 0,
    watchTime: 0,
    credits: 0
  });
  const [socialPlatforms, setSocialPlatforms] = useState<SocialPlatform[]>([]);
  const [isLoadingData, setIsLoadingData] = useState(true);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/landing" />;
  }

  if (isLoadingData) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="h-8 bg-muted rounded w-64 animate-pulse"></div>
              <div className="h-4 bg-muted rounded w-96 mt-2 animate-pulse"></div>
            </div>
            <div className="h-10 bg-muted rounded w-32 animate-pulse"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded animate-pulse"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-48 bg-muted rounded animate-pulse"></div>
            ))}
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Fetch user data and clips on component mount
  useEffect(() => {
    if (isAuthenticated) {
      fetchAllUserData();
    }
  }, [isAuthenticated]);

  const fetchAllUserData = async () => {
    setIsLoadingData(true);
    try {
      // Fetch all data in parallel
      const [clipsData, statsData, platformsData] = await Promise.all([
        getUserClips(0, 20),
        getUserStats(),
        getUserSocialPlatforms()
      ]);

      setClips(clipsData);
      setStats(statsData);
      setSocialPlatforms(platformsData);
    } catch (error) {
      console.error('Error fetching user data:', error);
    } finally {
      setIsLoadingData(false);
    }
  };

  // Use fetched social platforms data
  const connectedPlatforms = socialPlatforms;

  // Quick action cards
  const quickActions = [
    {
      title: "Create New",
      description: "Start a new video project",
      icon: Plus,
      link: "/smart-clipper",
      color: "text-green-500",
      bgColor: "bg-green-50 dark:bg-green-900/20"
    },
    {
      title: "My Clips",
      description: "View all your clips",
      icon: VideoIcon,
      link: "/clip-results",
      color: "text-blue-500",
      bgColor: "bg-blue-50 dark:bg-blue-900/20"
    },
    {
      title: "Analytics",
      description: "Performance insights",
      icon: BarChart,
      link: "/analytics",
      color: "text-purple-500",
      bgColor: "bg-purple-50 dark:bg-purple-900/20"
    },
    {
      title: "Schedule",
      description: "Content calendar",
      icon: Calendar,
      link: "/calendar",
      color: "text-orange-500",
      bgColor: "bg-orange-50 dark:bg-orange-900/20"
    }
  ];

  // Performance metrics using real data
  const performanceStats = [
    {
      title: "Total Views",
      value: stats.totalViews.toLocaleString(),
      change: "+12.5%",
      trend: "up" as const,
      icon: Eye,
      color: "text-blue-500"
    },
    {
      title: "Videos Created",
      value: stats.totalVideos.toString(),
      change: "+3",
      trend: "up" as const,
      icon: VideoIcon,
      color: "text-green-500"
    },
    {
      title: "Clips Generated",
      value: stats.totalClips.toString(),
      change: "+5",
      trend: "up" as const,
      icon: Scissors,
      color: "text-purple-500"
    },
    {
      title: "Credits Available",
      value: stats.credits.toString(),
      change: "Available",
      trend: "neutral" as const,
      icon: Zap,
      color: "text-yellow-500"
    }
  ];

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* YouTube Studio-style Header */}
        <div className="flex flex-col gap-4">
          <div>
            <h1 className="text-xl md:text-2xl font-semibold">Channel dashboard</h1>
            <p className="text-sm md:text-base text-muted-foreground">
              Welcome back, {user?.username || 'Creator'}! Here's how your content is performing.
            </p>
          </div>
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
            <div className="relative flex-1 sm:max-w-xs">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search your content..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 w-full"
              />
            </div>
            <Link to="/smart-clipper" className="w-full sm:w-auto">
              <Button className="bg-gradient-purple-blue hover:opacity-90 w-full sm:w-auto">
                <Plus className="h-4 w-4 mr-2" />
                Create
              </Button>
            </Link>
          </div>
        </div>

        {/* Performance Stats Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4">
          {performanceStats.map((stat, index) => (
            <Card key={index} className="hover:shadow-md transition-shadow">
              <CardContent className="p-3 md:p-6">
                <div className="flex items-center justify-between mb-2 md:mb-4">
                  <div className={`p-1.5 md:p-2 rounded-lg ${stat.color === 'text-blue-500' ? 'bg-blue-50 dark:bg-blue-900/20' :
                    stat.color === 'text-green-500' ? 'bg-green-50 dark:bg-green-900/20' :
                      stat.color === 'text-purple-500' ? 'bg-purple-50 dark:bg-purple-900/20' :
                        'bg-yellow-50 dark:bg-yellow-900/20'}`}>
                    <stat.icon className={`h-4 w-4 md:h-5 md:w-5 ${stat.color}`} />
                  </div>
                  {stat.trend !== 'neutral' && (
                    <div className={`flex items-center text-xs ${stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                      }`}>
                      {stat.trend === 'up' ? <ArrowUp className="h-3 w-3 mr-1" /> : <ArrowDown className="h-3 w-3 mr-1" />}
                      <span className="hidden sm:inline">{stat.change}</span>
                    </div>
                  )}
                </div>
                <div>
                  <p className="text-lg md:text-2xl font-bold mb-1">{stat.value}</p>
                  <p className="text-xs md:text-sm text-muted-foreground">{stat.title}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4 md:space-y-6">
          <TabsList className="grid w-full grid-cols-4 h-auto p-1">
            <TabsTrigger value="overview" className="text-xs md:text-sm py-2">Overview</TabsTrigger>
            <TabsTrigger value="content" className="text-xs md:text-sm py-2">Content</TabsTrigger>
            <TabsTrigger value="analytics" className="text-xs md:text-sm py-2">Analytics</TabsTrigger>
            <TabsTrigger value="settings" className="text-xs md:text-sm py-2">Settings</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {/* Latest Clips Carousel */}
            <div>
              <div className="flex items-center justify-between mb-3 md:mb-4">
                <h2 className="text-lg md:text-xl font-semibold">Latest uploads</h2>
                <Link to="/clip-results">
                  <Button variant="outline" size="sm" className="text-xs md:text-sm">
                    View all
                  </Button>
                </Link>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4">
                {clips.slice(0, 4).map((clip) => (
                  <Card key={clip.id} className="group hover:shadow-md transition-all relative overflow-hidden">
                    <div className="relative">
                      <img
                        src={clip.thumbnail}
                        alt={clip.title}
                        className="w-full h-32 object-cover"
                      />
                      {/* Status indicator */}
                      <div className="absolute top-2 left-2">
                        <Badge
                          variant={clip.status === 'published' ? 'default' :
                            clip.status === 'processing' ? 'secondary' : 'outline'}
                          className="text-xs"
                        >
                          {clip.status === 'published' && <CheckCircle className="h-3 w-3 mr-1" />}
                          {clip.status === 'processing' && <Clock className="h-3 w-3 mr-1" />}
                          {clip.status === 'draft' && <Edit className="h-3 w-3 mr-1" />}
                          {clip.status}
                        </Badge>
                      </div>
                      {/* Duration */}
                      <div className="absolute bottom-2 right-2">
                        <Badge variant="outline" className="text-xs bg-black/70 text-white border-none">
                          {clip.duration}
                        </Badge>
                      </div>
                      {/* Hover overlay */}
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center">
                        <Button
                          size="sm"
                          className="opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <Play className="h-4 w-4 mr-2" />
                          Preview
                        </Button>
                      </div>
                    </div>
                    <CardContent className="p-3">
                      <h3 className="font-medium text-sm line-clamp-2 mb-2">{clip.title}</h3>
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <span>{clip.platform}</span>
                        <div className="flex items-center gap-3">
                          <span className="flex items-center gap-1">
                            <Eye className="h-3 w-3" />
                            {clip.views.toLocaleString()}
                          </span>
                          <span className="flex items-center gap-1">
                            <Heart className="h-3 w-3" />
                            {clip.likes}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* Connected Platforms Section */}
            <div className="grid lg:grid-cols-2 gap-6">
              {/* Social Platforms */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Connected platforms</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {connectedPlatforms.map((platform) => (
                    <div key={platform.name} className="flex items-center justify-between p-3 rounded-lg border">
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-lg ${platform.bgColor}`}>
                          <span className="text-lg">{platform.icon}</span>
                        </div>
                        <div>
                          <h3 className="font-medium">{platform.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            {platform.connected ? `${platform.followers} followers` : "Not connected"}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {platform.connected ? (
                          <>
                            <Badge variant="outline" className="text-xs">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Connected
                            </Badge>
                            <Button variant="ghost" size="sm">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </>
                        ) : (
                          <Link to="/social-integration">
                            <Button size="sm" variant="outline">
                              Connect
                            </Button>
                          </Link>
                        )}
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Quick actions</CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-2 gap-3">
                  {quickActions.map((action) => (
                    <Link key={action.title} to={action.link}>
                      <Card className="hover:shadow-md transition-all cursor-pointer border-dashed hover:border-solid">
                        <CardContent className="p-4 text-center">
                          <div className={`p-3 rounded-lg ${action.bgColor} mx-auto w-fit mb-3`}>
                            <action.icon className={`h-5 w-5 ${action.color}`} />
                          </div>
                          <h3 className="font-medium text-sm mb-1">{action.title}</h3>
                          <p className="text-xs text-muted-foreground">
                            {action.description}
                          </p>
                        </CardContent>
                      </Card>
                    </Link>
                  ))}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Content Tab */}
          <TabsContent value="content" className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold">Content library</h2>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-2" />
                  Filter
                </Button>
                <Button variant="outline" size="sm">
                  Sort by: Latest
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {clips.map((clip) => (
                <Card key={clip.id} className="group hover:shadow-md transition-all">
                  <div className="relative">
                    <img
                      src={clip.thumbnail}
                      alt={clip.title}
                      className="w-full h-40 object-cover rounded-t-lg"
                    />
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center rounded-t-lg">
                      <Button
                        size="sm"
                        className="opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <Play className="h-4 w-4 mr-2" />
                        Preview
                      </Button>
                    </div>
                    <div className="absolute top-2 right-2">
                      <Badge variant="outline" className="text-xs bg-black/70 text-white border-none">
                        {clip.duration}
                      </Badge>
                    </div>
                  </div>
                  <CardContent className="p-4">
                    <h3 className="font-medium mb-2 line-clamp-2">{clip.title}</h3>
                    <div className="flex items-center justify-between text-sm text-muted-foreground mb-3">
                      <span>{clip.platform}</span>
                      <span>{clip.createdAt}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-4">
                        <span className="flex items-center gap-1">
                          <Eye className="h-4 w-4" />
                          {clip.views.toLocaleString()}
                        </span>
                        <span className="flex items-center gap-1">
                          <Heart className="h-4 w-4" />
                          {clip.likes}
                        </span>
                        <span className="flex items-center gap-1">
                          <MessageCircle className="h-4 w-4" />
                          {clip.comments}
                        </span>
                      </div>
                      <Button variant="ghost" size="sm">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <div className="text-center py-12">
              <BarChart className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">Analytics Dashboard</h3>
              <p className="text-muted-foreground mb-4">
                Detailed analytics and insights for your content performance
              </p>
              <Link to="/analytics">
                <Button>View Full Analytics</Button>
              </Link>
            </div>
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings" className="space-y-6">
            <div className="text-center py-12">
              <Settings className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">Channel Settings</h3>
              <p className="text-muted-foreground mb-4">
                Manage your channel preferences and configurations
              </p>
              <Link to="/settings">
                <Button>Open Settings</Button>
              </Link>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default HomePage;
