
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Dark mode colors (default) */
    --background: 240 10% 4%;
    --foreground: 210 40% 98%;

    --card: 240 10% 8%;
    --card-foreground: 210 40% 98%;

    --popover: 240 10% 8%;
    --popover-foreground: 210 40% 98%;

    --primary: 270 60% 52%;
    --primary-foreground: 210 40% 98%;

    --secondary: 220 30% 20%;
    --secondary-foreground: 210 40% 98%;

    --muted: 240 10% 20%;
    --muted-foreground: 215 20% 65%;

    --accent: 270 60% 52%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 240 10% 15%;
    --input: 240 10% 15%;
    --ring: 270 60% 52%;

    --radius: 0.5rem;

    --sidebar-background: 240 10% 6%;
    --sidebar-foreground: 240 5% 85%;
    --sidebar-primary: 270 60% 52%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 10% 12%;
    --sidebar-accent-foreground: 240 5% 85%;
    --sidebar-border: 240 10% 12%;
    --sidebar-ring: 270 60% 52%;
  }
  
  .light-mode {
    /* Light mode colors */
    --background: 0 0% 98%;
    --foreground: 240 10% 4%;
    
    --card: 0 0% 100%;
    --card-foreground: 240 10% 4%;
    
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 4%;
    
    --primary: 270 60% 52%;
    --primary-foreground: 0 0% 98%;
    
    --secondary: 220 20% 92%;
    --secondary-foreground: 240 10% 4%;
    
    --muted: 210 20% 92%;
    --muted-foreground: 215 20% 45%;
    
    --accent: 270 60% 52%;
    --accent-foreground: 0 0% 98%;
    
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    
    --border: 220 20% 86%;
    --input: 220 20% 86%;
    --ring: 270 60% 52%;
    
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 10% 30%;
    --sidebar-primary: 270 60% 52%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 220 20% 95%;
    --sidebar-accent-foreground: 240 10% 30%;
    --sidebar-border: 220 20% 90%;
    --sidebar-ring: 270 60% 52%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground transition-colors duration-150;
  }

  .gradient-text {
    @apply bg-gradient-purple-blue text-transparent bg-clip-text;
  }
}

@keyframes pulse-opacity {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.animate-pulse-opacity {
  animation: pulse-opacity 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  transform: translateY(20px);
  opacity: 0;
  animation: slideUp 0.5s forwards;
}

@keyframes slideUp {
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Additional transitions for smooth theme switching */
.transition-theme {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;
}

/* Hide scrollbars for TikTok-style feed */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Mobile-first responsive design utilities */
@media (max-width: 768px) {
  /* Mobile navigation adjustments */
  .mobile-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 50;
    background: hsl(var(--background));
    border-top: 1px solid hsl(var(--border));
    padding: 0.5rem;
  }

  /* Mobile video player adjustments */
  .mobile-video-container {
    height: 50vh;
    min-height: 300px;
  }

  /* Mobile timeline adjustments */
  .mobile-timeline {
    height: 80px;
    overflow-x: auto;
    overflow-y: hidden;
  }

  /* Mobile card adjustments */
  .mobile-card {
    margin: 0.5rem;
    padding: 1rem;
  }

  /* Mobile text sizing */
  .mobile-text-sm {
    font-size: 0.875rem;
  }

  .mobile-text-xs {
    font-size: 0.75rem;
  }

  /* Mobile button adjustments */
  .mobile-btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }

  /* Mobile grid adjustments */
  .mobile-grid-1 {
    grid-template-columns: 1fr;
  }

  /* Mobile spacing */
  .mobile-space-y-4 > * + * {
    margin-top: 1rem;
  }

  .mobile-space-y-6 > * + * {
    margin-top: 1.5rem;
  }

  /* Mobile padding */
  .mobile-p-4 {
    padding: 1rem;
  }

  .mobile-px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .mobile-py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
}

/* Tablet adjustments */
@media (min-width: 769px) and (max-width: 1024px) {
  .tablet-grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  .tablet-text-base {
    font-size: 1rem;
  }
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  /* Increase touch targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Remove hover effects on touch devices */
  .hover\:shadow-md:hover {
    box-shadow: none;
  }

  .hover\:scale-105:hover {
    transform: none;
  }

  /* Add active states for touch */
  .touch-active:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }
}

/* High DPI display adjustments */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .high-dpi-border {
    border-width: 0.5px;
  }
}

/* Landscape mobile adjustments */
@media (max-width: 768px) and (orientation: landscape) {
  .landscape-mobile-h-screen {
    height: 100vh;
  }

  .landscape-mobile-video {
    height: 70vh;
  }
}
