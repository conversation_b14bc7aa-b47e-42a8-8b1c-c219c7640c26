import React, { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Download,
  Edit,
  Play,
  Clock,
  TrendingUp,
  Search,
  Filter,
  Grid,
  List,
  Share2,
  Trash2,
  Star,
  Heart,
  MessageCircle,
  Youtube,
  Instagram,
  Twitter,
} from "lucide-react";
import { toast } from "@/hooks/use-toast";
import DashboardLayout from "@/components/Dashboard";
import { useAuth } from "@/context/AuthContext";
import { Navigate } from "react-router-dom";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { supabase } from "@/lib/supabase";
import TikTokFeed from "@/components/TikTokFeed";
import { getUserClips, type UserClip } from "@/services/userService";

// Use the UserClip interface from the service
type VideoClip = UserClip;

// Share Modal Component
const ShareModal = ({ clip, isOpen, onClose }: {
  clip: VideoClip;
  isOpen: boolean;
  onClose: () => void;
}) => {
  const handleShare = async (platform: string) => {
    try {
      // Get auth token from localStorage or context
      const token = localStorage.getItem('access_token');

      const response = await fetch(`http://localhost:8000/share/${platform}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          clipId: clip.id,
          clipUrl: clip.url,
          title: clip.title,
        }),
      });

      const result = await response.json();

      if (result.success) {
        toast({
          title: "Shared successfully",
          description: `Your clip has been shared to ${platform}`,
        });
        onClose();
      } else {
        toast({
          title: "Share not available",
          description: result.message || `${platform} sharing is not yet implemented`,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Share failed",
        description: `Failed to share to ${platform}. Please try again.`,
        variant: "destructive",
      });
    }
  };

  const platforms = [
    { name: 'YouTube', icon: Youtube, color: 'text-red-500', id: 'youtube' },
    { name: 'Instagram', icon: Instagram, color: 'text-pink-500', id: 'instagram' },
    { name: 'TikTok', icon: Play, color: 'text-black', id: 'tiktok' },
    { name: 'X (Twitter)', icon: Twitter, color: 'text-blue-400', id: 'twitter' },
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Share Clip</DialogTitle>
          <DialogDescription>
            Choose a platform to share your clip
          </DialogDescription>
        </DialogHeader>
        <div className="grid grid-cols-2 gap-4 py-4">
          {platforms.map((platform) => (
            <Button
              key={platform.id}
              variant="outline"
              className="h-20 flex flex-col gap-2"
              onClick={() => handleShare(platform.id)}
            >
              <platform.icon className={`h-6 w-6 ${platform.color}`} />
              <span className="text-sm">{platform.name}</span>
            </Button>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
};

const ClipResults = () => {
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [clips, setClips] = useState<VideoClip[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("vitalityScore");
  const [filterBy, setFilterBy] = useState("all");
  const [viewMode, setViewMode] = useState<"feed" | "grid">("feed"); // Changed default to feed
  const [selectedClips, setSelectedClips] = useState<string[]>([]);
  const [session, setSession] = useState<any>(null);
  const [shareModalOpen, setShareModalOpen] = useState(false);
  const [selectedClipForShare, setSelectedClipForShare] = useState<VideoClip | null>(null);

  useEffect(() => {
    const fetchSession = async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      setSession(session);
    };
    fetchSession();
  }, []);

  const user = session?.user;

  useEffect(() => {
    const loadClips = async () => {
      if (!user) {
        // Only attempt to load clips if user is available
        setLoading(false); // Stop loading animation if user is not available yet
        return;
      }

      try {
        setLoading(true);

        const userId = user.id;

        const { data, error } = await supabase
          .from("myclip")
          .select("*")
          .eq("user_id", userId)
          .order("created_at", { ascending: false });

        if (error) throw error;

        const formattedClips: VideoClip[] = data.map((clip: any) => {
          const durationSeconds = clip.end_time - clip.start_time;
          return {
            id: clip.id.toString(),
            url: clip.url,
            title: clip.text || "Untitled",
            duration: `${Math.round(durationSeconds)}s`,
            vitalityScore: Math.round(parseFloat(clip.score)) || 0,
            thumbnail: clip.url, // Use video URL as thumbnail fallback
            createdAt: clip.created_at,
            platform: clip.platform || "SmartClips",
            views: Math.floor(Math.random() * 10000), // Mock views for demo
            likes: Math.floor(Math.random() * 1000), // Mock likes for demo
            comments: Math.floor(Math.random() * 100), // Mock comments for demo
            status: "published" as const,
            videoId: clip.video_id || 0
          };
        });

        setClips(formattedClips);
      } catch (error) {
        console.error(error);
        toast({
          title: "Error loading clips",
          description: "Could not load clips for this user.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    loadClips();
  }, [user]); // Add user as a dependency here

  if (!isAuthenticated) {
    return <Navigate to="/login" />;
  }

  const filteredAndSortedClips = clips
    .filter((clip) => {
      const matchesSearch = clip.title
        .toLowerCase()
        .includes(searchTerm.toLowerCase());
      const matchesFilter = filterBy === "all" || clip.platform === filterBy;
      return matchesSearch && matchesFilter;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "vitalityScore":
          return b.vitalityScore - a.vitalityScore; // High to low virality
        case "duration":
          return parseInt(a.duration) - parseInt(b.duration);
        case "downloads":
          return b.downloadCount - a.downloadCount;
        case "recent":
          return (
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
        default:
          return b.vitalityScore - a.vitalityScore; // Default to virality
      }
    });

  const handleEdit = (clipId: string) => {
    navigate(`/clip-editor/${clipId}`);
  };

  const handleShare = (clipId: string) => {
    const clip = clips.find(c => c.id === clipId);
    if (clip) {
      // Copy clip URL to clipboard
      navigator.clipboard.writeText(clip.url).then(() => {
        toast({
          title: "Link copied!",
          description: "Clip link has been copied to your clipboard.",
        });
      }).catch(() => {
        toast({
          title: "Share failed",
          description: "Could not copy link to clipboard.",
          variant: "destructive",
        });
      });
    }
  };

  const handleDownload = (clipId: string) => {
    const clip = clips.find(c => c.id === clipId);
    if (clip) {
      // Create a temporary link to download the video
      const link = document.createElement('a');
      link.href = clip.url;
      link.download = `${clip.title}.mp4`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: "Download started",
        description: "Your clip is being downloaded.",
      });
    }
  };

  const handleDownload = async (clipId: string) => {
    try {
      const clip = clips.find((c) => c.id === clipId);
      if (!clip) return;

      toast({
        title: "Download started",
        description: `Downloading ${clip.title}...`,
      });

      setClips((prev) =>
        prev.map((c) =>
          c.id === clipId ? { ...c, downloadCount: c.downloadCount + 1 } : c
        )
      );
    } catch (error) {
      toast({
        title: "Download failed",
        description: "Failed to download the clip",
        variant: "destructive",
      });
    }
  };

  const handleBatchDownload = async () => {
    if (selectedClips.length === 0) {
      toast({
        title: "No clips selected",
        description: "Please select clips to download",
        variant: "destructive",
      });
      return;
    }

    toast({
      title: "Batch download started",
      description: `Downloading ${selectedClips.length} clips...`,
    });
  };

  const handleShare = (clip: VideoClip) => {
    setSelectedClipForShare(clip);
    setShareModalOpen(true);
  };

  const getVitalityBadge = (score: number) => {
    if (score >= 90)
      return {
        variant: "default" as const,
        label: "Viral",
        color: "bg-green-500",
      };
    if (score >= 80)
      return {
        variant: "secondary" as const,
        label: "High",
        color: "bg-blue-500",
      };
    if (score >= 70)
      return {
        variant: "outline" as const,
        label: "Good",
        color: "bg-yellow-500",
      };
    return { variant: "outline" as const, label: "Low", color: "bg-gray-500" };
  };

  const getPlatformIcon = (platform: string) => {
    const icons = {
      youtube: "🎥",
      tiktok: "🎵",
      instagram: "📸",
      twitter: "🐦",
      facebook: "👥",
    };
    return icons[platform as keyof typeof icons] || "📹";
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="container mx-auto py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto py-8 px-4">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold">MyClips</h1>
              <p className="text-muted-foreground">
                {clips.length} clips • Sorted by virality
              </p>
            </div>

            <div className="flex gap-2">
              <Button onClick={() => navigate("/smart-clipper")}>
                Create New Clips
              </Button>
            </div>
          </div>

          {/* Filters and Search */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search clips..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                <div className="flex gap-2">
                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger className="w-40">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="vitalityScore">
                        Vitality Score
                      </SelectItem>
                      <SelectItem value="recent">Most Recent</SelectItem>
                      <SelectItem value="duration">Duration</SelectItem>
                      <SelectItem value="downloads">Downloads</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={filterBy} onValueChange={setFilterBy}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Platforms</SelectItem>
                      <SelectItem value="youtube">YouTube</SelectItem>
                      <SelectItem value="tiktok">TikTok</SelectItem>
                      <SelectItem value="instagram">Instagram</SelectItem>
                      <SelectItem value="twitter">Twitter</SelectItem>
                    </SelectContent>
                  </Select>

                  <div className="flex border rounded-md">
                    <Button
                      variant={viewMode === "feed" ? "default" : "ghost"}
                      size="sm"
                      onClick={() => setViewMode("feed")}
                    >
                      <Play className="h-4 w-4" />
                    </Button>
                    <Button
                      variant={viewMode === "grid" ? "default" : "ghost"}
                      size="sm"
                      onClick={() => setViewMode("grid")}
                    >
                      <Grid className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Clips Display */}
          {filteredAndSortedClips.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <Play className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No clips found</h3>
                <p className="text-muted-foreground mb-4">
                  {searchTerm || filterBy !== "all"
                    ? "Try adjusting your search or filters"
                    : "Create your first video clips to get started"}
                </p>
                <Button onClick={() => navigate("/smart-clipper")}>
                  Create Clips
                </Button>
              </CardContent>
            </Card>
          ) : viewMode === "feed" ? (
            // TikTok-style vertical feed with autoplay
            <TikTokFeed
              clips={filteredAndSortedClips}
              onEdit={handleEdit}
              onShare={handleShare}
              onDownload={handleDownload}
            />
          ) : (
            // Grid view (fallback)
            <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3">
              {filteredAndSortedClips.map((clip) => {
                const vitalityBadge = getVitalityBadge(clip.vitalityScore);

                return (
                  <Card key={clip.id} className="relative transition-all overflow-hidden flex flex-col h-full">
                    {/* Video Thumbnail */}
                    <div className="relative flex-shrink-0">
                      <video
                        src={clip.url}
                        className="w-full aspect-video rounded-t-lg object-cover"
                        poster={clip.thumbnail}
                        controls
                      />
                      <div className="absolute top-2 left-2">
                        <Badge variant="secondary" className="text-xs">
                          {getPlatformIcon(clip.platform)} {clip.platform}
                        </Badge>
                      </div>
                      <div className="absolute top-2 right-2">
                        <Badge
                          className={`text-xs ${
                            clip.vitalityScore > 80
                              ? "bg-green-500 text-white"
                              : clip.vitalityScore > 50
                              ? "bg-yellow-400 text-black"
                              : "bg-red-500 text-white"
                          }`}
                        >
                          {clip.vitalityScore}%
                        </Badge>
                      </div>
                      <div className="absolute bottom-2 right-2">
                        <Badge variant="outline" className="text-xs bg-black/50 text-white">
                          {clip.duration}
                        </Badge>
                      </div>
                    </div>

                    {/* Card Content */}
                    <div className="flex flex-col flex-1 p-4">
                      <div className="flex-1 min-h-[80px]">
                        <h3 className="font-medium line-clamp-2 mb-2">
                          {clip.title || "Untitled Clip"}
                        </h3>
                        <div className="flex flex-wrap items-center gap-2 text-sm text-muted-foreground">
                          <span className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {clip.duration}
                          </span>
                          <span className="flex items-center gap-1">
                            <TrendingUp className="h-3 w-3" />
                            {clip.vitalityScore}% viral
                          </span>
                          <span className="flex items-center gap-1">
                            <Download className="h-3 w-3" />
                            {clip.downloadCount}
                          </span>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="mt-4 grid grid-cols-3 gap-2 w-full">
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDownload(clip.id);
                          }}
                        >
                          <Download className="h-3 w-3 mr-1" />
                          Download
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEdit(clip.id);
                          }}
                        >
                          <Edit className="h-3 w-3 mr-1" />
                          Edit
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleShare(clip);
                          }}
                        >
                          <Share2 className="h-3 w-3 mr-1" />
                          Share
                        </Button>
                      </div>
                    </div>
                  </Card>
                );
              })}
            </div>
          )}
        </div>

        {/* Share Modal */}
        {selectedClipForShare && (
          <ShareModal
            clip={selectedClipForShare}
            isOpen={shareModalOpen}
            onClose={() => {
              setShareModalOpen(false);
              setSelectedClipForShare(null);
            }}
          />
        )}
      </div>
    </DashboardLayout>
  );
};

export default ClipResults;
